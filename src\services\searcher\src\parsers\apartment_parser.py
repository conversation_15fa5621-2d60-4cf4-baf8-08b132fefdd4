"""
Apartment Page Parser

This module handles parsing of individual Booking.com apartment pages.
Extracts detailed apartment information including size and rating.
"""

import re

import pandas as pd
from bs4 import BeautifulSoup


class ApartmentParser:
    """Parser for individual Booking.com apartment pages"""

    def __init__(self):
        self.selectors = {
            "rating": 'div[data-testid="review-score-right-component"] div:first-child',
            "size_info": 'div[data-testid="property-features"] div[data-testid="facility-text"]',
            "entire_apt_info": 'div[data-testid="property-features"] span[data-testid="property-features"]',
            "property_highlights": 'div[data-testid="property-highlights"]',
            "bedrooms_info": 'div[data-testid="property-features"], div[data-testid="room-info"]',
            "facilities": 'div[data-testid="facility-text"]',
        }

    def parse_apartment_page(
        self, html_content: str, apartment_name: str
    ) -> pd.DataFrame:
        """
        Parse an individual apartment page and extract detailed information.

        Args:
            html_content (str): The HTML content of the apartment page
            apartment_name (str): Name of the apartment from Excel file

        Returns:
            pd.DataFrame: DataFrame containing detailed apartment information
        """
        soup = BeautifulSoup(html_content, "html.parser")
        data = {
            "apartment_name": [
                apartment_name
            ],  # Use provided name instead of extracting
            "size": [self._extract_apartment_size(soup)],
            "rating": [self._extract_rating(soup)],
            "bedrooms": [self._extract_bedrooms(soup)],
            "has_washing_machine": [self._has_washing_machine(soup)],
            "has_kitchen": [self._has_kitchen_extractor(soup)],
            "has_breakfast": [self._has_breakfast(soup)],
            "post_code": [self._extract_post_code(soup)],
            "bathrooms": [self._extract_bathrooms(soup)],
        }
        return pd.DataFrame(data)

    def _extract_apartment_size(self, soup) -> str:
        """
        Extract apartment size from the page.
        Looks for size information in multiple locations.
        """
        try:
            # Try multiple patterns for size
            size_patterns = [
                r"(\d+)\s*m²(?:\s*size)?",  # "35 m² size" or "35 m²"
                r"(\d+)\s*sq\.?\s*m",  # "35 sq.m" or "35 sq m"
                r"(\d+)\s*(?:m2|m²)",  # "35m2" or "35m²"
                r"size:\s*(\d+)\s*m",  # "size: 35m"
                r"living\s*space:\s*(\d+)",  # "living space: 35"
            ]

            # Method 1: Look in property highlights
            highlights_div = soup.select_one(self.selectors["property_highlights"])
            if highlights_div:
                text = highlights_div.text.strip().lower()
                print(f"Full property highlights text: '{text}'")
                for pattern in size_patterns:
                    size_match = re.search(pattern, text)
                    if size_match:
                        return size_match.group(1)  # Return just the number

            # Method 2: Look for size information in property features
            size_features = soup.select(self.selectors["size_info"])
            print(f"Found {len(size_features)} size info elements")
            for feature in size_features:
                text = feature.text.strip().lower()
                print(f"Size info text: {text}")
                # Look for size patterns like "35 m²", "35m²", "35m2", "35 square meters"
                size_match = re.search(r"(\d+)\s*(?:m[²2]|square\s*meters?)", text)
                if size_match:
                    return size_match.group(1)  # Return just the number

            # Method 3: Look in entire apartment information
            entire_info = soup.select(self.selectors["entire_apt_info"])
            print(f"Found {len(entire_info)} entire apt info elements")
            for info in entire_info:
                text = info.text.strip().lower()
                print(f"Entire apt info text: {text}")
                size_match = re.search(r"(\d+)\s*(?:m[²2]|square\s*meters?)", text)
                if size_match:
                    return size_match.group(1)

            # Method 4: Search in hp--desc_highlights descendants
            highlights = soup.find(class_="hp--desc_highlights")
            if highlights:
                for element in highlights.descendants:
                    if hasattr(element, "text"):
                        text = element.text.strip().lower()
                        for pattern in size_patterns:
                            size_match = re.search(pattern, text)
                            if size_match:
                                return size_match.group(1)

            # Method 5: Search in hprt-facilities-block(Apartment type column subfield in table under availability) descendants
            highlights = soup.find(class_="hprt-facilities-block")
            if highlights:
                for element in highlights.descendants:
                    if hasattr(element, "text"):
                        text = element.text.strip().lower()
                        for pattern in size_patterns:
                            size_match = re.search(pattern, text)
                            if size_match:
                                return size_match.group(1)

            return "N/A"
        except Exception as e:
            print(f"Error extracting size: {e}")
            return "N/A"

    def _extract_rating(self, soup) -> str:
        """Extract rating from the page"""
        try:
            # Try multiple selectors to find the rating
            selectors = [
                self.selectors["rating"],
                "div.review-score-widget div.review-score-badge",
                'div[data-testid="review-score"] div',
                "div.bui-review-score__badge",
                'div[data-testid="review-score-right-component"] div.b878730b30',
                'div[data-testid="review-score"] span',
                "div.d86cee9b25",  # New Booking.com class for ratings
            ]

            for selector in selectors:
                elem = soup.select_one(selector)
                if elem:
                    # Get the text and clean it
                    rating_text = elem.text.strip()
                    print(
                        f"Found rating text: '{rating_text}' using selector: {selector}"
                    )

                    # Try to extract a decimal number (e.g., "8.7")
                    decimal_match = re.search(r"(\d+\.\d+)", rating_text)
                    if decimal_match:
                        return decimal_match.group(1)

                    # Try to extract just digits
                    digits = "".join(c for c in rating_text if c.isdigit() or c == ".")
                    if digits:
                        # If it's a single digit or already has a decimal point
                        if len(digits) == 1 or "." in digits:
                            return digits

                        # If it's 2 digits (like "87"), convert to "8.7"
                        if len(digits) == 2:
                            return f"{float(digits) / 10:.1f}"

                        # If it's more digits, assume it's out of 1000
                        return f"{float(digits) / 100:.1f}"

            # If we still haven't found a rating, look in the meta tags
            for meta in soup.find_all("meta"):
                if meta.get("itemprop") == "ratingValue":
                    rating = meta.get("content")
                    if rating:
                        return rating

            return "N/A"
        except Exception as e:
            print(f"Error extracting rating: {e}")
            return "N/A"

    def _extract_bedrooms(self, soup) -> str:
        """
        Extract number of bedrooms from the page.
        Looks for bedroom information in property features.
        """
        try:
            # Look for bedroom information in property features
            for feature in soup.select(self.selectors["bedrooms_info"]):
                text = feature.text.strip().lower()
                # Look for bedroom patterns like "2 bedrooms" or "2 bedroom"
                bedroom_match = re.search(r"(\d+)\s*bedroom", text)
                if bedroom_match:
                    return bedroom_match.group(1)  # Return just the number

                # Look for "entire place" with bedroom count in URL
                scripts = soup.find_all("script")
                for script in scripts:
                    if script.string and "entire_place_bedroom_count" in script.string:
                        bedroom_match = re.search(
                            r"entire_place_bedroom_count%3D(\d+)", script.string
                        )
                        if bedroom_match:
                            return bedroom_match.group(1)

            # If still not found, search in property_highlights_wrapper descendants
            highlights = soup.find(class_="hp--desc_highlights")
            if highlights:
                for element in highlights.descendants:
                    if hasattr(element, "text"):
                        text = element.text.strip().lower()
                        bedroom_match = re.search(r"(\d+)\s*bedroom", text)
                        if bedroom_match:
                            return bedroom_match.group(1)

            return "N/A"
        except Exception as _ex:
            return "N/A"

    def _has_kitchen_extractor(self, soup) -> bool:
        """
        Check if the apartment has a kitchen extractor.
        Returns True if a kitchen extractor/hood is listed in the facilities/amenities.
        """
        try:
            facilities = soup.select(self.selectors["facilities"])
            for facility in facilities:
                text = facility.text.strip().lower()
                if any(term in text for term in ["kitchen facilities", "kitchen"]):
                    return True

            # Also check in property highlights
            highlights = soup.find(class_=["property_hightlights_wrapper"])
            if highlights:
                text = highlights.text.strip().lower()
                if any(term in text for term in ["kitchen facilities", "kitchen"]):
                    return True

            # Also check in hprt-facilities-block descendants
            # (section in apartment type column of the table under Availability)
            highlights = soup.find(class_=["hprt-facilities-block"])
            if highlights and hasattr(highlights, "text"):
                text = highlights.text.strip().lower()
                if any(term in text for term in ["kitchen", "private kitchen"]):
                    return True

            return False
        except Exception as e:
            print(f"Error checking for kitchen extractor: {e}")
            return False

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if text:
            # Remove extra whitespace and normalize
            return " ".join(text.strip().split())
        return "N/A"

    def _has_washing_machine(self, soup) -> bool:
        """
        Check if the apartment has a washing machine.
        Returns True if a washing machine is listed in the facilities/amenities.
        """
        try:
            facilities = soup.select(self.selectors["facilities"])
            for facility in facilities:
                text = facility.text.strip().lower()
                if "washing machine" in text or "washer" in text:
                    return True

            # Also check in property highlights
            highlights = soup.select_one(self.selectors["property_highlights"])
            if highlights:
                text = highlights.text.strip().lower()
                if "washing machine" in text or "washer" in text:
                    return True

            # If still not found, search in hprt-facilities-others(section in apartment type column of the table under Availability) descendants
            highlights = soup.find(class_="hprt-facilities-others")
            if highlights and hasattr(highlights, "text"):
                text = highlights.text.strip().lower()
                if "washing machine" in text or "washer" in text:
                    return True

            # If still not found, search in hp_desc_main_content (main text) descendants
            highlights = soup.find(class_="hp_desc_main_content")
            if highlights and hasattr(highlights, "text"):
                text = highlights.text.strip().lower()
                if "washing machine" in text or "washer" in text:
                    return True

            return False
        except Exception as e:
            print(f"Error checking for washing machine: {e}")
            return False

    def _has_breakfast(self, soup) -> bool:
        """
        Check if the apartment has breakfast available.
        Returns True if breakfast is listed in the property-most-popular-facilities-wrapper.
        """
        try:
            # Check in the property-most-popular-facilities-wrapper class
            popular_facilities = soup.find(class_="hp--popular_facilities")
            if popular_facilities and hasattr(popular_facilities, "text"):
                text = popular_facilities.text.strip().lower()
                if "breakfast" in text:
                    return True
            return False
        except Exception as e:
            print(f"Error checking for breakfast: {e}")
            return False

    def _extract_bathrooms(self, soup) -> str:
        """
        Extract number of bathrooms from the page.
        First checks property_hightlights_wrapper for 'X bathrooms' pattern,
        then checks hprt-facilities-block for private/attached bathroom mentions.
        """
        try:
            # First check in property_hightlights_wrapper
            highlights = soup.find(class_="property_hightlights_wrapper")
            if highlights and hasattr(highlights, "text"):
                text = highlights.text.strip().lower()
                bathroom_match = re.search(r"(\d+)\s*bathrooms", text)
                if bathroom_match:
                    return bathroom_match.group(1)

            # Then check in hprt-facilities-block
            facilities = soup.find(class_="hprt-facilities-block")
            if facilities and hasattr(facilities, "text"):
                text = facilities.text.strip().lower()
                if "private bathroom" in text or "attached bathroom" in text:
                    return "1"

            return "N/A"
        except Exception as e:
            print(f"Error extracting bathrooms: {e}")
            return "N/A"

    def _extract_post_code(self, soup) -> str:
        """
        Extract post code from the PropertyHeaderAddressDesktop-wrapper.
        Returns the 5-digit post code if found, 'N/A' otherwise.
        """
        try:
            address_div = soup.find(
                "div", attrs={"data-testid": "PropertyHeaderAddressDesktop-wrapper"}
            )
            if address_div:
                text = address_div.text.strip()
                # Look for a 5-digit number
                post_code_match = re.search(r"\b\d{5}\b", text)
                if post_code_match:
                    return post_code_match.group(0)
            return "N/A"
        except Exception as e:
            print(f"Error extracting post code: {e}")
            return "N/A"
