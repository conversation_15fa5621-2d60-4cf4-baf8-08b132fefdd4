FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install uv using the official method
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy pyproject.toml for better caching
COPY pyproject.toml ./

# Install system dependencies required for <PERSON><PERSON> in non-headless mode
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    xvfb \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcups2 \
    libdbus-1-3 \
    libgdk-pixbuf2.0-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    libgbm1 \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Install dependencies using uv
# RUN uv pip install --no-cache-dir -e .

# Copy pyproject.toml and uv.lock (if it exists) for better caching
COPY pyproject.toml ./
# COPY uv.lock* ./

# Copy the rest of the application BEFORE running uv sync
COPY . .

# Install dependencies using uv (without virtual environment)
RUN uv pip install --system -r pyproject.toml

# Install playwright and browser
RUN uv pip install --system playwright && \
    playwright install chromium && \
    playwright install-deps

# Copy the rest of the application
COPY . .

# Set display for non-headless browser
ENV DISPLAY=:99

# Create data directory
RUN mkdir -p /app/data/html

# Set environment variables
ENV PYTHONPATH=/app
ENV BROWSER_HEADLESS=false
ENV BROWSER_OUTPUT_DIR=/app/data/html
ENV BROWSER_SCRAPPER_IMPLEMENTATION=src.services.browser_service,BrowserService
ENV BROWSER_STORE_IMPLEMENTATION=src.stores.browser_scrapper_store_path,BrowserScrapperStorePath
ENV BROWSER_DELAY_IN_MILLISECONDS_MIN=200
ENV BROWSER_DELAY_IN_MILLISECONDS_MAX=1000
ENV BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS=30000
ENV BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS=5000
ENV BROWSER_MAX_CONCURRENT=2
ENV BOOKING_AREA=Psiri

# Expose port for API
EXPOSE 8000

# Create entrypoint script that starts Xvfb before running the application
RUN echo '#!/bin/bash\nXvfb :99 -screen 0 1280x800x16 &\nsleep 1\npython -m src.main serve "$@"' > /entrypoint.sh \
    && chmod +x /entrypoint.sh

# Run the application using the entrypoint script
ENTRYPOINT ["/entrypoint.sh"]
