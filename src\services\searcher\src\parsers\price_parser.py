"""
Price Parser Module

This module provides functionality for extracting and parsing price information
from text, handling different currency formats and number representations.

Usage:
    from src.shared.parsers.price_parser import PriceParser, PriceInfo

    parser = PriceParser()
    price_info = parser.parse("€120.50")

    # Access the parsed information
    value = price_info.value  # Decimal('120.50')
    currency = price_info.currency  # "EUR"

    # Format the price
    formatted = price_info.format()  # "€120.50"
"""

import re
import logging
from enum import Enum
from decimal import Decimal
from typing import Dict, Optional, Protocol, Pattern
from pydantic import BaseModel, Field, field_validator, model_validator

# Configure logging
logger = logging.getLogger(__name__)

class PriceParsingStatus(str, Enum):
    """Enum representing the status of price parsing."""
    SUCCESS = "success"
    INVALID_FORMAT = "invalid_format"
    MISSING_VALUE = "missing_value"
    ZERO_VALUE = "zero_value"
    ERROR = "error"


class PriceInfo(BaseModel):
    """
    Model representing parsed price information.

    Attributes:
        value: The numeric price value as a Decimal
        currency: The currency code (e.g., "EUR", "USD")
        status: The parsing status
        original_text: The original price text that was parsed
        error_message: Optional error message if parsing failed
    """
    value: Optional[Decimal] = Field(
        None,
        description="The numeric price value"
    )
    currency: Optional[str] = Field(
        None,
        description="The currency code (e.g., 'EUR', 'USD')"
    )
    status: PriceParsingStatus = Field(
        PriceParsingStatus.SUCCESS,
        description="The parsing status"
    )
    original_text: str = Field(
        ...,
        description="The original price text that was parsed"
    )
    error_message: Optional[str] = Field(
        None,
        description="Error message if parsing failed"
    )

    @field_validator('value')
    @classmethod
    def validate_value(cls, v: Optional[Decimal]) -> Optional[Decimal]:
        """Validate that the price value is non-negative if present."""
        if v is not None and v < 0:
            raise ValueError("Price value cannot be negative")
        return v

    @model_validator(mode='after')
    def validate_status_consistency(self) -> 'PriceInfo':
        """Ensure the status is consistent with the presence of a value."""
        if self.status == PriceParsingStatus.SUCCESS and self.value is None:
            self.status = PriceParsingStatus.MISSING_VALUE

        if self.status == PriceParsingStatus.SUCCESS and self.value == Decimal('0'):
            self.status = PriceParsingStatus.ZERO_VALUE

        return self

    def is_valid(self) -> bool:
        """Check if the price information is valid (has a non-zero value)."""
        return (
            self.status == PriceParsingStatus.SUCCESS and
            self.value is not None and
            self.value > 0
        )

    def format(self, include_currency: bool = True) -> str:
        """
        Format the price with its currency symbol.

        Args:
            include_currency: Whether to include the currency symbol

        Returns:
            Formatted price string
        """
        if self.value is None:
            return "N/A"

        # Format the numeric value
        formatted_value = f"{self.value:.2f}"

        # Add currency symbol if available and requested
        if include_currency and self.currency:
            currency_symbols = {
                "EUR": "€",
                "USD": "$",
                "GBP": "£",
                # Add more currency symbols as needed
            }
            symbol = currency_symbols.get(self.currency, self.currency)
            return f"{symbol}{formatted_value}"

        return formatted_value

    class Config:
        """Pydantic model configuration."""
        json_schema_extra = {
            "example": {
                "value": 120.50,
                "currency": "EUR",
                "status": "success",
                "original_text": "€120.50",
                "error_message": None
            }
        }


class PriceParserProtocol(Protocol):
    """Protocol defining the interface for price parsers."""

    def parse(self, price_text: str) -> PriceInfo:
        """
        Parse price information from text.

        Args:
            price_text: The text containing price information

        Returns:
            PriceInfo: The parsed price information
        """
        ...


class PriceParser:
    """
    Parser for extracting and formatting price information from text.

    This parser handles different currency formats and number representations,
    including various currency symbols and decimal/thousands separators.
    """

    # Currency symbol to code mapping
    CURRENCY_SYMBOLS: Dict[str, str] = {
        "€": "EUR",
        "$": "USD",
        "£": "GBP",
        "¥": "JPY",
        "₽": "RUB",
        # Add more currency symbols as needed
    }

    # Currency code to symbol mapping (for formatting)
    CURRENCY_CODES: Dict[str, str] = {
        "EUR": "€",
        "USD": "$",
        "GBP": "£",
        "JPY": "¥",
        "RUB": "₽",
        # Add more currency codes as needed
    }

    # Regular expressions for currency detection
    CURRENCY_SYMBOL_PATTERN: Pattern = re.compile(r'([€$£¥₽])')
    CURRENCY_CODE_PATTERN: Pattern = re.compile(r'\b(EUR|USD|GBP|JPY|RUB)\b')

    def __init__(self):
        """Initialize the price parser."""
        pass

    def parse(self, price_text: str) -> PriceInfo:
        """
        Parse price information from text.

        Args:
            price_text: The text containing price information

        Returns:
            PriceInfo: The parsed price information
        """
        if not price_text or price_text.strip() == "N/A":
            return PriceInfo(
                value=None,
                currency=None,
                status=PriceParsingStatus.MISSING_VALUE,
                original_text=price_text,
                error_message="Empty or N/A price text"
            )

        try:
            # Extract currency
            currency = self._extract_currency(price_text)

            # Extract numeric value
            value = self._extract_numeric_value(price_text)

            # Create price info
            status = PriceParsingStatus.SUCCESS if value is not None else PriceParsingStatus.MISSING_VALUE

            return PriceInfo(
                value=value,
                currency=currency,
                status=status,
                original_text=price_text,
                error_message=None if value is not None else "Could not extract numeric value"
            )

        except Exception as e:
            logger.error(f"Error parsing price text '{price_text}': {str(e)}")
            return PriceInfo(
                value=None,
                currency=None,
                status=PriceParsingStatus.ERROR,
                original_text=price_text,
                error_message=str(e)
            )

    def _extract_currency(self, price_text: str) -> Optional[str]:
        """
        Extract currency information from price text.

        Args:
            price_text: The text containing price information

        Returns:
            Optional[str]: The currency code if found, None otherwise
        """
        # Check for currency symbols
        symbol_match = self.CURRENCY_SYMBOL_PATTERN.search(price_text)
        if symbol_match:
            symbol = symbol_match.group(1)
            return self.CURRENCY_SYMBOLS.get(symbol)

        # Check for currency codes
        code_match = self.CURRENCY_CODE_PATTERN.search(price_text)
        if code_match:
            return code_match.group(1)

        return None

    def _extract_numeric_value(self, price_text: str) -> Optional[Decimal]:
        """
        Extract numeric value from price text.

        This method handles different number formats:
        - 1,234.56 (US/UK format)
        - 1.234,56 (European format)

        Args:
            price_text: The text containing price information

        Returns:
            Optional[Decimal]: The numeric value if found, None otherwise
        """
        # Remove currency symbols and other non-numeric characters
        # Keep only digits, dots, and commas
        cleaned_text = ''.join(c for c in price_text if c.isdigit() or c in '.,')

        if not cleaned_text:
            return None

        try:
            # Handle US/UK format (1,234.56)
            if '.' in cleaned_text:
                # Remove thousands separators (commas)
                us_format = cleaned_text.replace(',', '')
                return Decimal(us_format)

            # Special case for European format with both dots and commas (1.234,56)
            if ',' in cleaned_text and '.' in cleaned_text:
                # European format typically has dots as thousands separators and comma as decimal
                # Check if it matches the pattern: digits.digits,digits
                if re.match(r'^\d+\.\d+,\d+$', cleaned_text):
                    # Replace thousands separators (dots) with nothing and decimal separator (comma) with dot
                    eu_format = cleaned_text.replace('.', '').replace(',', '.')
                    return Decimal(eu_format)
                else:
                    # It's probably US format with extra commas
                    us_format = cleaned_text.replace(',', '')
                    return Decimal(us_format)

            # Handle simple European format with comma as decimal (e.g., 123,45)
            elif ',' in cleaned_text:
                # Replace decimal separator (comma) with dot
                simple_eu_format = cleaned_text.replace(',', '.')
                return Decimal(simple_eu_format)

            # Handle plain numbers
            return Decimal(cleaned_text)

        except Exception as e:
            logger.warning(f"Failed to parse numeric value from '{cleaned_text}': {str(e)}")
            return None
