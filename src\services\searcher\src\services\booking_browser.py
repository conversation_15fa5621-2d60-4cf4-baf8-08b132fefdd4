"""
Browser automation module for Booking.com apartment search.
Handles browser interaction with Booking.com and property data extraction.
"""

import logging
import os
import random
from datetime import datetime, timedelta

from playwright.async_api import (
    TimeoutError as PlaywrightTimeoutError,
)
from playwright.async_api import (
    async_playwright,
)

from src.utils.common_utils import fix_windows_event_loop_policy

BOOKING_BASE_URL: str = "https://www.booking.com"

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BookingBrowser:
    """Handle browser automation for Booking.com."""

    def __init__(self):
        self.headless = False  # Force non-headless to avoid detection
        self.browser = None
        self.context = None
        self.page = None
        self.base_url = BOOKING_BASE_URL

        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        ]

        self.min_delay = float(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MIN", 200))
        self.max_delay = float(os.getenv("BROWSER_DELAY_IN_MILLISECONDS_MAX", 1000))
        self.page_timeout = int(os.getenv("BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS", "30000"))
        self.click_timeout = int(os.getenv("BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS", "5000"))

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def start(self):
        """Start the browser with anti-detection measures."""
        logger.info("Starting browser")
        # Fix Windows event loop policy before using Playwright
        fix_windows_event_loop_policy()
        self.playwright = await async_playwright().start()

        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-features=IsolateOrigins,site-per-process",
                "--disable-site-isolation-trials",
            ],
        )

        user_agent = random.choice(self.user_agents)
        logger.info(f"Using user agent: {user_agent}")

        self.context = await self.browser.new_context(
            viewport={"width": 1280, "height": 800},
            user_agent=user_agent,
            locale="en-US",
            timezone_id="Europe/Athens",
            has_touch=False,
            java_script_enabled=True,
            ignore_https_errors=False,
            extra_http_headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive",
            },
        )

        await self.context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });
        """)

        self.page = await self.context.new_page()

    async def close(self):
        """Close browser and cleanup."""
        logger.info("Closing browser")
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def random_delay(self, min_factor=1.0, max_factor=1.0):
        """Add random delay to simulate human behavior."""
        delay = random.randint(
            int(self.min_delay * min_factor), int(self.max_delay * max_factor)
        )
        logger.debug(f"Adding random delay of {delay}ms")
        await self.page.wait_for_timeout(delay)

    async def wait_for_load_state(self, state="load"):
        """Wait for the page to load to a specific state."""
        logger.debug(f"Waiting for page load state: {state}")
        await self.page.wait_for_load_state(state, timeout=self.page_timeout)
        await self.random_delay(0.5, 1.25)
        logger.debug(f"Page ({state}) loaded")

    async def go_to_url(self, url):
        """Navigate to a specific URL."""
        logger.info(f"Navigating to {url}")
        await self.page.goto(url)
        await self.wait_for_load_state()

    async def navigate_to_booking(self):
        """Navigate to Booking.com homepage."""
        await self.go_to_url(self.base_url)

        try:
            await self.page.click(
                'button[id="onetrust-accept-btn-handler"]', timeout=self.click_timeout
            )
            logger.info("Accepted cookies")
        except PlaywrightTimeoutError:
            logger.info("No cookie dialog found or already accepted")

    async def search_apartments(
        self, location, check_in="auto", check_out="auto", guests=2, bedrooms=2
    ):
        """Perform apartment search."""
        try:
            if check_in == "auto" or check_out == "auto":
                check_in_date, check_out_date = self._calculate_dates()
                check_in = check_in_date.strftime("%Y-%m-%d")
                check_out = check_out_date.strftime("%Y-%m-%d")

            logger.info(
                f"Search parameters: Location: {location} Check-in: {check_in}, Check-out: {check_out}, Guests: {guests}"
            )

            # Create search URL
            neighborhood = location.split(",")[0].strip()
            direct_url = (
                f"{self.base_url}/searchresults.html"
                f"?ss={neighborhood}"
                f"&checkin={check_in}"
                f"&checkout={check_out}"
                f"&group_adults={guests}"
                "&group_children=0"
                "&no_rooms=1"
                "&selected_currency=EUR"
                f"&nflt=entire_place_bedroom_count%3D{bedrooms}"
            )

            logger.info(f"Using direct URL: {direct_url}")

            # Navigate to search results
            await self.navigate_to_booking()

            await self.go_to_url(direct_url)

            # Try to expand filters to show more options
            await self._expand_filters()
            await self.random_delay(0.5, 1.0)

            return "searchresults" in self.page.url

        except Exception as e:
            logger.error(f"Error during search: {e}")
            return False

    async def _expand_filters(self):
        """Try to expand all filter options and panels."""
        try:
            logger.debug("Trying to expand filters")
            # Try to open the filters panel if it exists
            filter_buttons = [
                'button:has-text("Filters")',
                'button:has-text("Filter by:")',
                '[data-testid="filters-button"]',
            ]

            for selector in filter_buttons:
                try:
                    logger.debug(f"Trying filter button with selector: {selector}")
                    button = await self.page.query_selector(selector)
                    if button:
                        logger.debug(f"Found filter button with selector: {selector}")
                        await button.click(timeout=self.click_timeout)
                        await self.random_delay(0.1, 0.1)
                        break
                except Exception:
                    continue

            # Try to expand all filter sections
            expand_buttons = [
                'button:has-text("Show all")',
                'button:has-text("More")',
                '[data-testid="filters-expand-button"]',
                '[aria-expanded="false"]',
            ]

            for selector in expand_buttons:
                try:
                    buttons = await self.page.query_selector_all(selector)
                    logger.debug(
                        f"Clicking expand buttons ({len(buttons)}) with selector: {selector}"
                    )
                    for button in buttons:
                        await button.click(timeout=self.click_timeout)
                        await self.random_delay(0.1, 0.1)
                except Exception:
                    continue

        except Exception as e:
            logger.error(f"Error expanding filters: {e}")

    def _calculate_dates(self):
        """Calculate next Monday and Thursday dates."""
        today = datetime.now()
        days_until_monday = (0 - today.weekday()) % 7
        if days_until_monday == 0:
            days_until_monday = 7
        next_monday = today + timedelta(days=days_until_monday)
        next_thursday = next_monday + timedelta(days=3)
        return next_monday, next_thursday

    async def get_search_results(self, location="", period_name="", guests=2):
        """Get search results by scrolling until all available properties are found."""
        all_results = []
        seen_properties = set()
        scroll_attempts = 0
        max_scroll_attempts = 5  # Reduced scroll attempts

        try:
            logger.info("Started getting search results")
            # Extract total number of properties from the header
            await self.wait_for_load_state()
            total_text = await self.page.text_content("h1")
            if total_text and "properties found" in total_text:
                target_properties = int("".join(filter(str.isdigit, total_text)))
                logger.info(f"Found {target_properties} total properties in {location}")
            # else:
            #     target_properties = 75  # fallback number
            #     logger.warning(
            #         f"Could not find total properties count, using fallback: {target_properties}"
            #     )

            while (
                len(seen_properties) < target_properties
                and scroll_attempts < max_scroll_attempts
            ):
                await self.wait_for_load_state()

                # Get current properties
                property_cards = await self.page.query_selector_all(
                    'div[data-testid="property-card"]'
                )
                logger.info(
                    f"Found {len(property_cards)} total property cards (unique so far: {len(seen_properties)})"
                )

                # Process new properties
                new_properties = []
                for card in property_cards:
                    try:
                        name = await self._extract_text(
                            card, 'div[data-testid="title"]'
                        )
                        if name == "N/A" or name in seen_properties:
                            continue

                        seen_properties.add(name)
                        new_properties.append(
                            {
                                "name": name,
                                "address": await self._extract_text(
                                    card, 'span[data-testid="address"]'
                                ),
                                "rating": await self._extract_text(
                                    card, 'div[data-testid="review-score"] div'
                                ),
                                "price": await self._extract_text(
                                    card,
                                    'span[data-testid="price-and-discounted-price"]',
                                ),
                                "size": await self._extract_text(
                                    card,
                                    'span[data-testid="recommended-units-unit-configuration"]',
                                ),
                                "link": await self._extract_link(
                                    card, 'a[data-testid="title-link"]'
                                ),
                            }
                        )

                    except Exception as e:
                        logger.error(f"Error extracting property details: {e}")

                if new_properties:
                    all_results.extend(new_properties)
                    logger.info(f"Added {len(new_properties)} new unique properties")

                # Try to load more results
                await self._try_load_more_results()
                scroll_attempts += 1

                if len(new_properties) == 0:
                    logger.info("No new properties found in this scroll")

            logger.info(
                f"Found {len(seen_properties)} unique properties in {scroll_attempts} scroll attempts"
            )
            return all_results

        except Exception as e:
            logger.error(f"Error getting search results: {e}")
            return all_results

    async def _try_load_more_results(self):
        """Try various methods to load more results."""
        try:
            # Try to find and click "Show more" buttons
            show_more_selectors = [
                'button:has-text("Show more results")',
                'button:has-text("Load more")',
                '[data-testid="show-more-button"]',
                ".show_more_button",
                "button.show-more",
                'button:has-text("See more properties")',
            ]

            for selector in show_more_selectors:
                try:
                    button = await self.page.wait_for_selector(selector, timeout=self.click_timeout)
                    if button:
                        logger.debug(
                            f"Found show more button with selector: {selector}"
                        )
                        await button.click(timeout=self.click_timeout)
                        await self.wait_for_load_state()
                        return
                except Exception:
                    continue

            # Scroll in sections
            viewport = await self.page.evaluate("""() => ({
                width: document.documentElement.clientWidth,
                height: document.documentElement.clientHeight,
                scrollHeight: document.documentElement.scrollHeight
            })""")

            scroll_sections = 2  # Reduced scroll sections
            for i in range(scroll_sections):
                scroll_to = int((i + 1) * viewport["scrollHeight"] / scroll_sections)
                await self.page.evaluate(f"window.scrollTo(0, {scroll_to})")
                await self.random_delay(0.5, 1.0)

        except Exception as e:
            logger.error(f"Error trying to load more results: {e}")

    async def _extract_text(self, element, selector):
        """Extract text from element using selector."""
        try:
            text_element = await element.query_selector(selector)
            if text_element:
                return await text_element.text_content()
        except Exception:
            pass
        return "N/A"

    async def _extract_link(self, element, selector):
        """Extract link from element using selector."""
        try:
            link_element = await element.query_selector(selector)
            if link_element:
                href = await link_element.get_attribute("href")
                if href:
                    return self.base_url + href if href.startswith("/") else href
        except Exception:
            pass
        return "N/A"
