"""
Browser Scrapper Store Azure

This module provides an Azure File Share implementation of the browser scrapper store.
"""

import os
from datetime import datetime

from azure.storage.fileshare import ShareDirectoryClient, ShareFileClient

from src.stores.browser_scrapper_store_abstract import BrowserScrapperStoreAbstract


class BrowserScrapperStoreAzure(BrowserScrapperStoreAbstract):
    """
    Azure File Share implementation of the browser scrapper store.
    """
    
    def __init__(self, output_dir: str) -> None:
        """
        Initialize the Azure File Share storage.

        Args:
            output_dir: The directory path within the file share
            connection_string: Azure Storage account connection string
            share_name: Name of the Azure file share
        """
        super().__init__(output_dir)
        self.connection_string = os.getenv("AZURE_STORAGE_CONNECTION_STRING", None)
        self.share_name = os.getenv("AZURE_STORAGE_FILE_SHARE_NAME", None)

    def save_content(self, url: str, content: str, file_extension: str, sub_dir: str = None) -> str:
        """
        Save string content to Azure File Share.

        Args:
            url: The URL that was scraped
            content: The content to save
            file_extension: The file extension to use
            sub_dir: The subdirectory to save the content to
            
        Returns:
            str: The path where the content was saved in Azure
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        url_domain: str = url.split("://")[1].split("/")[0]
        if url_domain.startswith("www."):
            url_domain = url_domain[4:]
        dir_path: str = f"{self.output_dir}/{url_domain}"
        if sub_dir:
            dir_path = f"{dir_path}/{sub_dir}"
        file_path = f"{dir_path}/{timestamp}{file_extension}"

        directory_client = ShareDirectoryClient.from_connection_string(
            conn_str=self.connection_string,
            share_name=self.share_name,
            directory_path=str(self.output_dir),
        )
        if not directory_client.exists():
            directory_client.create_directory()
        if sub_dir:
            sub_dir_parts: list[str] = sub_dir.split("/")
            cur_sub_dir = url_domain
            for part in sub_dir_parts:
                cur_sub_dir = f"{cur_sub_dir}/{part}"
                if not directory_client.get_subdirectory_client(cur_sub_dir).exists():
                    directory_client.create_subdirectory(cur_sub_dir)

        file_client = ShareFileClient.from_connection_string(
            conn_str=self.connection_string,
            share_name=self.share_name,
            file_path=file_path,
        )
        file_client.upload_file(content.encode("utf-8"))

        return f"{self.share_name}/{file_path}"
