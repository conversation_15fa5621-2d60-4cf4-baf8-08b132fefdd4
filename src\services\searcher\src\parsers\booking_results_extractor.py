"""
Booking.com Results Extractor

Description:
    Extracts information from Booking.com search results HTML files and outputs to JSON.
    Captures search period, guest count, area, and details for each apartment listing.

Usage:
    python booking_results_extractor.py input.html output.json

Arguments:
    input.html  : Path to HTML file containing Booking.com search results
    output.json : Path where to save the extracted data

Dependencies:
    - BeautifulSoup4 for HTML parsing
    - SearchResultsParser from parsers.search_parser for property data extraction

Output Format:
    {
        "search_period": {
            "check_in": "Thu, May 9",
            "check_out": "Sat, May 11"
        },
        "number_of_guests": 2,
        "area": "Psiri",
        "results": [
            {
                "apartment_name": "Name",
                "price": "150.00",
                "rating": "8.5",
                "link": "url",
                "bedrooms": "2",
                "bathrooms": "1",
                "has_kitchen": true
            }
        ]
    }
"""

import json
from typing import Dict

from bs4 import BeautifulSoup

from src.parsers.search_parser import SearchResultsParser


class BookingResultsExtractor:
    """Extracts booking information from search results HTML files"""

    def __init__(self):
        self.search_parser = SearchResultsParser()
        self.selectors = {
            "dates": 'button[data-testid="searchbox-dates-container"] span[data-testid="date-display-field-start"], button[data-testid="searchbox-dates-container"] span[data-testid="date-display-field-end"]',
            "guests": 'button[data-testid="occupancy-config"]',
            "area": 'input[name="ss"]',
        }

    def _extract_dates(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract check-in and check-out dates from the page"""
        try:
            date_buttons = soup.select(self.selectors["dates"])
            if len(date_buttons) >= 2:
                # Format: "Thu, May 9 - Sat, May 11" or similar
                check_in = date_buttons[0].text.strip()
                check_out = date_buttons[1].text.strip()
                return {"check_in": check_in, "check_out": check_out}
            return {"check_in": "N/A", "check_out": "N/A"}
        except Exception as e:
            print(f"Error extracting dates: {e}")
            return {"check_in": "N/A", "check_out": "N/A"}

    def _extract_guests(self, soup: BeautifulSoup) -> int:
        """Extract number of guests from the page"""
        try:
            guests_button = soup.select_one(self.selectors["guests"])
            if guests_button:
                # Extract first number from text like "2 adults · 0 children · 1 room"
                guests_text = guests_button.text.strip()
                import re

                # Find first number that represents adults
                match = re.search(r"(\d+)\s+adults?", guests_text.lower())
                if match:
                    return int(match.group(1))
            return 0
        except Exception as e:
            print(f"Error extracting guests count: {e}")
            return 0

    def _extract_area(self, soup: BeautifulSoup) -> str:
        """Extract area/location from the page"""
        try:
            area_input = soup.select_one(self.selectors["area"])
            if area_input and area_input.get("value"):
                return area_input.get("value").strip()

            # Fallback: look for the input with type="search"
            area_input = soup.find("input", {"type": "search"})
            if area_input and area_input.get("value"):
                return area_input.get("value").strip()

            return "N/A"
        except Exception as e:
            print(f"Error extracting area: {e}")
            return "N/A"

    def process_html_content(self, html_content: str) -> dict[str, any]:
        """
        Process a Booking.com search results HTML content and return results as dictionary

        Args:
            html_content (str): The HTML content to process

        Returns:
            dict[str, any]: dict containing the extracted information
        """
        try:
            if not html_content:
                raise ValueError("HTML content is empty")

            # Parse HTML
            soup = BeautifulSoup(html_content, "html.parser")

            # Extract all required information
            dates = self._extract_dates(soup)
            guests = self._extract_guests(soup)
            area = self._extract_area(soup)
            properties = self.search_parser.parse_search_page(html_content)

            # Prepare output data
            output_data = {
                "search_period": dates,
                "number_of_guests": guests,
                "area": area,
                "results": properties,
            }

            return output_data

        except Exception as e:
            print(f"Error processing HTML content: {e}")
            return False

    def process_html_file(self, html_file_path: str, output_json_path: str) -> bool:
        """
        Process a Booking.com search results HTML file and save results as JSON

        Args:
            html_file_path (str): Path to the HTML file to process
            output_json_path (str): Path where to save the JSON output

        Returns:
            bool: True if processing was successful, False otherwise
        """
        try:
            # Read HTML file
            with open(html_file_path, "r", encoding="utf-8") as file:
                html_content = file.read()

            # Prepare output data
            output_data = self.process_html_content(html_content)

            # Save to JSON file
            with open(output_json_path, "w", encoding="utf-8") as file:
                json.dump(output_data, file, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            print(f"Error processing HTML file: {e}")
            return False


def main():
    """Example usage of BookingResultsExtractor"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Extract booking information from HTML file"
    )
    parser.add_argument("input_file", help="Path to input HTML file")
    parser.add_argument("output_file", help="Path to output JSON file")

    args = parser.parse_args()

    extractor = BookingResultsExtractor()
    success = extractor.process_html_file(args.input_file, args.output_file)

    if success:
        print(
            f"Successfully processed {args.input_file} and saved results to {args.output_file}"
        )
    else:
        print("Failed to process the file")


if __name__ == "__main__":
    main()
