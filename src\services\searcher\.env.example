# API Server Configuration
PORT=8000
HOST="0.0.0.0"

# Browser Configuration
BROWSER_HEADLESS="false"
BROWSER_SCRAPPER_IMPLEMENTATION="src.services.browser_scrapper_playwright,Browser<PERSON>crapperPlaywright"
BROWSER_STORE_IMPLEMENTATION="src.stores.browser_scrapper_store_path,BrowserScrapperStorePath"
# BROWSER_STORE_IMPLEMENTATION="src.stores.browser_scrapper_store_azure,BrowserScrapperStoreAzure"
BROWSER_OUTPUT_DIR="./data/html"
BROWSER_DELAY_IN_MILLISECONDS_MIN="200"
BROWSER_DELAY_IN_MILLISECONDS_MAX="1000"
BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS="30000"
BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS="5000"
BROWSER_MAX_CONCURRENT="2"

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING="<Azure storage account connection string>"
AZURE_STORAGE_FILE_SHARE_NAME="<Azure file share name>"

# Booking.com Search Configuration
BOOKING_AREA="Psiri"
BOOKING_GUESTS="2"
# BOOKING_CHECK_IN="2025-06-01"  # Auto-calculated if not provided
# BOOKING_CHECK_OUT="2025-06-08"  # Auto-calculated if not provided
