# Searcher Service Configuration

This directory contains configuration files for the Searcher service.

## Purpose

- Stores configuration settings for the Searcher service
- Defines parameters for browser automation and scraping
- Contains environment-specific settings
- Manages rate limiting and request throttling parameters

## Expected Content

This directory will contain:
- Configuration files in YAML, JSON, or other formats
- Environment-specific configuration overrides
- Browser settings for Playwright/Puppeteer
- User agent configurations
- Rate limiting and throttling parameters
- Proxy configuration (if applicable)

## Current Configuration

The service currently uses environment variables for configuration. Key settings include:

### Browser Configuration
- `BROWSER_HEADLESS`: Run browser in headless mode (true/false)
- `BROWSER_DELAY_IN_MILLISECONDS_MIN`: Minimum delay between browser actions (default: 200ms)
- `BROWSER_DELAY_IN_MILLISECONDS_MAX`: Maximum delay between browser actions (default: 1000ms)
- `BROWSER_PAGE_TIMEOUT_IN_MILLISECONDS`: Timeout for page operations (default: 30000ms)
- `BROWSER_CLICK_TIMEOUT_IN_MILLISECONDS`: Timeout for click operations (default: 5000ms)
- `BROWSER_MAX_CONCURRENT`: Maximum number of concurrent browser instances (default: 2)

### Search Configuration
- `BOOKING_AREA`: Default search area (default: "Psiri")
- `BOOKING_GUESTS`: Default number of guests (default: 2)
- `BROWSER_OUTPUT_DIR`: Directory to save HTML content (default: "./data/html")

### Service Configuration
- `PORT`: API server port (default: 8000)
- `HOST`: API server host (default: "0.0.0.0")

### API Configuration
- All API responses use type-safe Pydantic models for validation
- Search status tracking is enabled by default with real-time updates
- Response models ensure consistent data types across all endpoints

## Configuration Guidelines

The configuration files should include settings for:
- Browser behavior (headless mode, viewport size, etc.)
- Request patterns and intervals to avoid detection
- Retry strategies for failed requests
- Output directory structures
- Logging levels and formats
- Performance tuning parameters

## Notes

- Configuration files should not contain sensitive information
- Environment-specific settings should be parameterized
- Changes to configuration should be documented
- Default values should be reasonable for most use cases
