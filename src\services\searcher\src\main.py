"""
Searcher Service - Main Module

This service is responsible for scraping booking.com for rental unit data.
It provides both a REST API and a CLI interface.
"""

# Fix Windows event loop policy at the very beginning
import sys

if sys.platform == "win32":
    import asyncio

    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

import calendar
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
import os
import sys
import asyncio
from typing import Optional

import click
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI

from src.utils.common_utils import get_period_weeks, fix_windows_event_loop_policy

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.controllers.search_controller import router as search_router
from src.services.search_service import SearchService
from src.services.period_search_service import PeriodSearchService
from src.models.booking_search_request import BookingSearchRequest

# Load environment variables
load_dotenv()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI lifespan event handler.

    Configures the Windows event loop policy to support subprocess operations
    required by Playwright.
    """
    # Startup
    fix_windows_event_loop_policy()
    yield
    # Shutdown (nothing to do)


# Create FastAPI app
app = FastAPI(
    title="AirPrice Searcher Service",
    description="Service for scraping booking.com for rental unit data",
    version="1.0.0",
    lifespan=lifespan,
)


# Add routers
app.include_router(search_router, prefix="/api/v1", tags=["search"])


# CLI progress callback function
def cli_progress_callback(
    week_num: int, total_weeks: int, period_name: str, success: Optional[bool]
):
    """
    Progress callback for CLI commands.

    Args:
        week_num: Current week number
        total_weeks: Total number of weeks
        period_name: Name of the period being searched
        success: None for start, True for success, False for failure
    """
    if success is None:
        # Starting search
        click.echo(f"[{week_num}/{total_weeks}] Starting search for {period_name}")
    elif success:
        # Successful search
        click.echo(f"[{week_num}/{total_weeks}] ✓ Completed search for {period_name}")
    else:
        # Failed search
        click.echo(
            f"[{week_num}/{total_weeks}] ✗ Failed search for {period_name}", err=True
        )


# CLI Commands
@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """AirPrice Searcher Service CLI"""
    if ctx.invoked_subcommand is None:
        # If no subcommand is specified, run the API server
        ctx.invoke(serve)


@cli.command(name="serve")
@click.option(
    "--host",
    default="0.0.0.0",
    help="Host to bind the server to",
)
@click.option(
    "--port",
    default=8000,
    type=int,
    help="Port to bind the server to",
)
def serve(host: str, port: int):
    """Run the API server"""
    # Fix Windows event loop policy before starting the server
    fix_windows_event_loop_policy()
    # Disable reload for Windows compatibility with subprocess operations
    uvicorn.run("src.main:app", host=host, port=port, reload=False)


@cli.command(name="search")
@click.option(
    "--area",
    required=True,
    envvar="BOOKING_AREA",
    help="Booking search area (can also be set via BOOKING_AREA env var)",
)
@click.option(
    "--guests",
    type=int,
    required=True,
    default=2,
    envvar="BOOKING_GUESTS",
    help="Booking search number of guests (can also be set via BOOKING_GUESTS env var, default: 2)",
)
@click.option(
    "--check-in",
    type=str,
    required=False,
    envvar="BOOKING_CHECK_IN",
    help="Booking search check in date in YYYY-MM-DD string format (can also be set via BOOKING_CHECK_IN env var)",
)
@click.option(
    "--check-out",
    type=str,
    required=False,
    envvar="BOOKING_CHECK_OUT",
    help="Booking search check in date in YYYY-MM-DD string format (can also be set via BOOKING_CHECK_OUT env var)",
)
@click.option(
    "--output-dir",
    type=click.Path(),
    default="./data/html",
    envvar="BROWSER_OUTPUT_DIR",
    help="Directory to save downloaded content (can also be set via BROWSER_OUTPUT_DIR env var, default: ./data/html)",
)
def search(
    area: str,
    guests: int,
    check_in: Optional[str],
    check_out: Optional[str],
    output_dir: str,
):
    """Perform a booking.com search and save the results"""
    check_in_dt = (
        datetime.strptime(check_in, "%Y-%m-%d") if check_in else datetime.today()
    )
    check_out_dt = (
        datetime.strptime(check_out, "%Y-%m-%d")
        if check_out
        else check_in_dt + timedelta(days=7)
    )

    # Convert datetime objects to strings for the model
    check_in_str = check_in_dt.strftime("%Y-%m-%d")
    check_out_str = check_out_dt.strftime("%Y-%m-%d")

    search_service = SearchService()
    request = BookingSearchRequest(
        area=area, guests=guests, check_in=check_in_str, check_out=check_out_str
    )

    # Run the search asynchronously
    async def run_cli_search():
        result = await search_service.search(request, output_dir)

        if result.success:
            click.echo(
                f"Search completed for {area} with {guests} guests for period {request.get_period_name()} in {result.elapsed_time:.2f} seconds"
            )
            click.echo(f"Results saved to: {result.output_path}")
        else:
            click.echo(
                f"Search failed for {area} with {guests} guests for period {request.get_period_name()} in {result.elapsed_time:.2f} seconds",
                err=True,
            )
            click.echo(f"Error: {result.error}", err=True)

    # Fix Windows event loop policy before running async code
    fix_windows_event_loop_policy()
    asyncio.run(run_cli_search())


@cli.command(name="search-period")
@click.option(
    "--area",
    required=True,
    envvar="BOOKING_AREA",
    help="Booking search area (can also be set via BOOKING_AREA env var)",
)
@click.option(
    "--guests",
    type=int,
    required=True,
    default=2,
    envvar="BOOKING_GUESTS",
    help="Booking search number of guests (can also be set via BOOKING_GUESTS env var, default: 2)",
)
@click.option(
    "--date-from",
    type=str,
    required=True,
    envvar="BOOKING_DATE_FROM",
    help="Booking search period from date in YYYY-MM-DD string format (can also be set via BOOKING_DATE_FROM env var)",
)
@click.option(
    "--date-upto",
    type=str,
    required=True,
    envvar="BOOKING_DATE_UPTO",
    help="Booking search period upto date in YYYY-MM-DD string format (can also be set via BOOKING_DATE_UPTO env var)",
)
@click.option(
    "--output-dir",
    type=click.Path(),
    default="./data/html",
    envvar="BROWSER_OUTPUT_DIR",
    help="Directory to save downloaded content (can also be set via BROWSER_OUTPUT_DIR env var, default: ./data/html)",
)
@click.option(
    "--max-concurrent",
    type=int,
    default=2,
    envvar="BROWSER_MAX_CONCURRENT",
    help="Maximum number of concurrent searches (can also be set via BROWSER_MAX_CONCURRENT env var, default: 2)",
)
def search_period(
    area: str,
    guests: int,
    date_from: Optional[str],
    date_upto: Optional[str],
    output_dir: str,
    max_concurrent: int,
):
    """Perform a booking.com period search and save the results"""

    date_from_dt = (
        datetime.strptime(date_from, "%Y-%m-%d")
        if date_from
        else datetime.today() + timedelta(days=1)
    )
    if date_from_dt <= datetime.today():
        date_from_dt = datetime.today() + timedelta(days=1)
    last_month_day = calendar.monthrange(date_from_dt.year, date_from_dt.month)[1]
    date_upto_dt = (
        datetime.strptime(date_upto, "%Y-%m-%d")
        if date_upto
        else date_from_dt.replace(day=last_month_day)
    )
    if date_upto_dt <= date_from_dt:
        date_upto_dt = date_from_dt + timedelta(days=1)
    weeks = get_period_weeks(date_from_dt, date_upto_dt)
    click.echo(f"Found {len(weeks)} weeks to search (max {max_concurrent} concurrent):")
    for i, (start, end) in enumerate(weeks, 1):
        click.echo(f"  {i}. {start.strftime('%Y-%m-%d')} to {end.strftime('%Y-%m-%d')}")

    if len(weeks) == 0:
        click.echo("No weeks found in the specified date range.")
        return

    first_week_start = weeks[0][0]
    if first_week_start <= datetime.today():
        first_week_start = datetime.today() + timedelta(days=1)
    weeks[0] = (first_week_start, weeks[0][1])

    # Create period search service
    period_search_service = PeriodSearchService()

    # Run the period search asynchronously
    click.echo(f"\nStarting period search for {area} with {guests} guests...")

    async def run_cli_period_search():
        result = await period_search_service.run_period_search(
            area, guests, weeks, output_dir, max_concurrent, cli_progress_callback
        )

        # Display summary
        click.echo("\nSearch summary:")
        click.echo(f"  Successful: {result.successful}")
        click.echo(f"  Failed: {result.failed}")
        click.echo(f"  Elapsed time: {result.elapsed_time:.2f} seconds")

        # Log any errors
        for error in result.errors:
            click.echo(f"  Error: {error}", err=True)

    # Fix Windows event loop policy before running async code
    fix_windows_event_loop_policy()
    asyncio.run(run_cli_period_search())
    click.echo(
        f"\nPeriod search completed for {area} with {guests} guests across {len(weeks)} weeks"
    )


if __name__ == "__main__":
    cli()
