"""
Booking.com Search Results Parser

Description:
    Parses property information from Booking.com search results pages.
    Used by BookingResultsExtractor to get details about each property listing.

Usage:
    from parsers.search_parser import SearchResultsParser

    parser = SearchResultsParser()
    properties = parser.parse_search_page(html_content)

Input:
    HTML content of a Booking.com search results page

Output:
    List of dictionaries containing property information:
    [
        {
            "apartment_name": "Name",
            "price": "150.00",
            "rating": "8.5",
            "link": "url",
            "bedrooms": "2",
            "bathrooms": "1",
            "has_kitchen": true
        }
    ]

Note:
    Uses BeautifulSoup4 for HTML parsing
    Extracts data using Booking.com's data-testid attributes
    Uses shared price parser for price extraction
"""

import logging
import re
from typing import Dict, List, Optional

from bs4 import BeautifulSoup

from src.parsers.price_parser import PriceParser

# Configure logging
logger = logging.getLogger(__name__)


class SearchResultsParser:
    """Parser for Booking.com search results pages"""

    def __init__(self):
        self.selectors = {
            "property_card": 'div[data-testid="property-card"]',
            "name": '[data-testid="title"]',
            "price": 'span[data-testid="price-and-discounted-price"]',
            "rating": (
                'div[data-testid="review-score"] div:first-child, '
                'div[data-testid="review-score"] span'
            ),
            "link": 'a[data-testid="title-link"]',
            "bedrooms": 'div[data-testid="property-card"] div[data-testid="room-info"] span',
            "availability": 'div[data-testid="availability-single"]',
        }
        self.price_parser = PriceParser()

    def _extract_bedrooms_from_url(self, html_content: str) -> str:
        """Extract number of bedrooms from URL in the HTML content"""
        try:
            soup = BeautifulSoup(html_content, "html.parser")
            # Look for meta tags or script tags containing URL
            for script in soup.find_all("script"):
                if script.string and "entire_place_bedroom_count" in script.string:
                    match = re.search(
                        r"entire_place_bedroom_count%3D(\d+)", script.string
                    )
                    if match:
                        return match.group(1)
            return "N/A"
        except Exception:
            return "N/A"

    def parse_search_page(self, html_content: str) -> List[Dict]:
        """Parse a search results page and extract apartment information"""
        soup = BeautifulSoup(html_content, "html.parser")
        properties = []

        # Find all property cards
        for card in soup.select(self.selectors["property_card"]):
            property_info = self._extract_property_info(card)
            if property_info:
                # If bedrooms not found in HTML, try URL
                if property_info["bedrooms"] == "N/A":
                    property_info["bedrooms"] = self._extract_bedrooms_from_url(
                        html_content
                    )
                properties.append(property_info)
        return properties

    def _extract_availability_info(self, card_element) -> Dict[str, bool]:
        """Extract bathroom count and kitchen availability from availability section"""
        try:
            availability = card_element.select_one(self.selectors["availability"])
            if availability and availability.text:
                text = availability.text.strip().lower()

                # Extract bathroom count
                bathroom_match = re.search(r"(\d+)\s*bathroom", text)
                bathrooms = bathroom_match.group(1) if bathroom_match else "N/A"

                # Check for kitchen
                has_kitchen = any(term in text for term in ["kitchen", "kitchenette"])

                return {"bathrooms": bathrooms, "has_kitchen": has_kitchen}
        except Exception as e:
            logger.error("Error extracting availability info: %s", str(e))

        return {"bathrooms": "N/A", "has_kitchen": False}

    def _extract_property_info(self, card_element) -> Optional[Dict]:
        """Extract information from a single property card"""
        try:
            # Extract basic information using selectors
            name_elem = card_element.select_one(self.selectors["name"])
            price_elem = card_element.select_one(self.selectors["price"])
            rating_elem = card_element.select_one(self.selectors["rating"])
            link_elem = card_element.select_one(self.selectors["link"])
            bedrooms_elem = card_element.select_one(self.selectors["bedrooms"])

            # Extract bathrooms and kitchen info
            availability_info = self._extract_availability_info(card_element)

            # Try to get bedrooms from room info element
            bedrooms = "N/A"
            if bedrooms_elem:
                room_text = bedrooms_elem.text.strip().lower()
                if "bedroom" in room_text:
                    bedrooms = "".join(filter(str.isdigit, room_text))

            return {
                "apartment_name": name_elem.text.strip() if name_elem else "N/A",
                "price": self._extract_price(price_elem.text) if price_elem else "N/A",
                "rating": self._extract_rating(rating_elem.text)
                if rating_elem
                else "N/A",
                "link": link_elem["href"] if link_elem else "N/A",
                "bedrooms": bedrooms,
                "bathrooms": availability_info["bathrooms"],
                "has_kitchen": availability_info["has_kitchen"],
            }
        except Exception as e:
            logger.error("Error parsing property card: %s", str(e))
            return None

    def _extract_price(self, price_text: str) -> str:
        """
        Extract and format price from text.

        Uses the shared price parser to extract the numeric value from the price text.
        Returns the value as a string for backward compatibility.

        Args:
            price_text: The text containing price information

        Returns:
            str: The extracted price as a string, or 'N/A' if extraction fails
        """
        try:
            # Use the shared price parser
            price_info = self.price_parser.parse(price_text)

            # Return the value as a string for backward compatibility
            if price_info.is_valid():
                return str(price_info.value)

            logger.warning(
                "Failed to extract price from '%s': %s", price_text, price_info.status
            )
            return "N/A"
        except Exception as e:
            logger.error("Error extracting price from '%s': %s", price_text, str(e))
            return "N/A"

    def _extract_rating(self, rating_text: str) -> str:
        """Extract and format rating from text"""
        try:
            if not rating_text or rating_text.strip() == "N/A":
                return "N/A"

            if "scored" in rating_text.lower():
                decimal_matches = re.findall(r"(\d+\.\d+)", rating_text)
                if decimal_matches:
                    return decimal_matches[0]

            if "." in rating_text:
                rating_match = re.search(r"(\d+\.\d+)", rating_text)
                if rating_match:
                    return rating_match.group(1)

            rating = "".join(c for c in rating_text if c.isdigit())

            if rating and rating.isdigit():
                # Format rating based on number of digits
                if len(rating) == 1:  # Single digit
                    return f"{rating}.0"
                if len(rating) == 2:  # Two digits
                    rating_float = float(rating) / 10
                    return f"{rating_float:.1f}"
                # More digits
                rating_float = float(rating) / 100
                return f"{rating_float:.1f}"

            return rating_text.strip() if rating_text else "N/A"
        except Exception as e:
            logger.error("Error extracting rating: %s", str(e))
            return "N/A"

    def get_properties_count(self, html_content: str) -> int:
        """Get total number of properties from search results page"""
        soup = BeautifulSoup(html_content, "html.parser")
        try:
            count_elem = soup.select_one('[data-testid="property-cards-count"]')
            if count_elem:
                return int("".join(filter(str.isdigit, count_elem.text)))
        except Exception:
            logger.warning(
                "Failed to extract property count from header, using card count"
            )
        return len(soup.select(self.selectors["property_card"]))
