"""
Booking Search Request Model

This module defines the data model for a booking.com search request.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, computed_field


class BookingSearchRequest(BaseModel):
    """
    Model representing a booking.com search request.
    """

    area: str = Field(..., description="The area to search for")
    guests: int = Field(2, description="Number of guests")
    check_in: str = Field(None, description="Check-in date in YYYY-MM-DD format")
    check_out: str = Field(None, description="Check-out date in YYYY-MM-DD format")

    def get_period_name(self) -> str:
        """
        Get a formatted period name for the search.
        """
        return f"{self.check_in} to {self.check_out}"

    def get_sub_directory(self) -> str:
        """
        Get the subdirectory path for storing search results.
        """
        return f"{self.area}/{self.guests}/{self.check_in}_{self.check_out}"


class BookingSearchPeriodRequest(BaseModel):
    """
    Model representing a booking.com period search request.
    """

    area: str = Field(..., description="The area to search for")
    guests: int = Field(2, description="Number of guests")
    date_from: datetime = Field(..., description="Start date of the period")
    date_upto: datetime = Field(..., description="End date of the period")
    max_concurrent: Optional[int] = Field(2, description="Maximum number of concurrent searches")

    def get_period_name(self) -> str:
        """
        Get a formatted period name for the search.
        """
        return f"{self.date_from.strftime('%Y-%m-%d')} to {self.date_upto.strftime('%Y-%m-%d')}"

    def get_sub_directory(self) -> str:
        """
        Get the subdirectory path for storing search results.
        """
        return f"{self.area}/{self.guests}/period_{self.date_from.strftime('%Y-%m-%d')}_{self.date_upto.strftime('%Y-%m-%d')}"


class SearchStatus(BaseModel):
    """
    Model representing the status of a search operation.
    """

    id: str = Field(..., description="Unique identifier for the search")
    area: str = Field(..., description="The area being searched")
    guests: str = Field(..., description="Number of guests")
    period: str = Field(..., description="Search period")
    status: str = Field(..., description="Current status of the search")
    started_at: str = Field(..., description="When the search was started")
    completed_at: Optional[str] = Field(None, description="When the search was completed")
    output_path: Optional[str] = Field(None, description="Path to the search results")
    error: Optional[str] = Field(None, description="Error message if search failed")

    @computed_field
    @property
    def elapsed_time(self) -> str:
        """
        Calculate elapsed time in seconds between started_at and completed_at.
        Returns the elapsed time as a string, or empty string if not completed.
        """
        if not self.completed_at:
            return ""

        try:
            started = datetime.fromisoformat(self.started_at)
            completed = datetime.fromisoformat(self.completed_at)
            seconds = (completed - started).total_seconds()
            return str(seconds)
        except (ValueError, TypeError):
            return ""


class SearchStatusResponse(BaseModel):
    """
    Model representing the response for search status endpoint.
    """

    searches: List[SearchStatus] = Field(..., description="List of search statuses")
