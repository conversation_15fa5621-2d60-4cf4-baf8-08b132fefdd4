"""
Test Search Service Error Handling

This module tests that the SearchService properly handles errors and updates
the SearchStatus objects with error information when searches fail.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from src.services.search_service import SearchService
from src.models.booking_search_request import BookingSearchRequest


class TestSearchServiceErrorHandling:
    """Test error handling in SearchService."""

    @pytest.fixture
    def search_service(self):
        """Create a SearchService instance for testing."""
        return SearchService()

    @pytest.fixture
    def sample_request(self):
        """Create a sample search request for testing."""
        return BookingSearchRequest(
            area="Test Area",
            guests=2,
            check_in="2025-07-01",
            check_out="2025-07-08"
        )

    @pytest.mark.asyncio
    async def test_search_error_updates_status(self, search_service, sample_request):
        """Test that search errors are properly recorded in SearchStatus."""
        # Mock the factory to raise an exception
        mock_factory = Mock()
        mock_scrapper = Mock()
        mock_scrapper.download_page = AsyncMock(side_effect=Exception("Test error message"))
        mock_factory.create_booking_search_scrapper.return_value = mock_scrapper
        
        search_service.factory = mock_factory

        # Perform the search (should fail)
        result = await search_service.search(sample_request, "./test_output")

        # Verify the search result indicates failure
        assert result.success is False
        assert result.error == "Test error message"
        assert result.output_path is None

        # Verify the search status was updated with error information
        statuses = search_service.get_search_statuses()
        assert len(statuses) == 1
        
        status = statuses[0]
        assert status.status == "failed"
        assert status.error == "Test error message"
        assert status.completed_at != ""  # Should have completion timestamp
        assert status.output_path == ""   # Should be empty for failed searches

    @pytest.mark.asyncio
    async def test_search_success_no_error(self, search_service, sample_request):
        """Test that successful searches don't have error information."""
        # Mock the factory to succeed
        mock_factory = Mock()
        mock_scrapper = Mock()
        mock_scrapper.download_page = AsyncMock(return_value="/test/output/path.html")
        mock_factory.create_booking_search_scrapper.return_value = mock_scrapper
        
        search_service.factory = mock_factory

        # Perform the search (should succeed)
        result = await search_service.search(sample_request, "./test_output")

        # Verify the search result indicates success
        assert result.success is True
        assert result.error is None
        assert result.output_path == "/test/output/path.html"

        # Verify the search status shows success with no error
        statuses = search_service.get_search_statuses()
        assert len(statuses) == 1
        
        status = statuses[0]
        assert status.status == "completed"
        assert status.error == ""  # Should be empty string for successful searches
        assert status.completed_at != ""  # Should have completion timestamp
        assert status.output_path == "/test/output/path.html"

    @pytest.mark.asyncio
    async def test_multiple_searches_with_mixed_results(self, search_service, sample_request):
        """Test that multiple searches with different outcomes are tracked correctly."""
        # Mock the factory to alternate between success and failure
        mock_factory = Mock()
        
        # First search succeeds
        mock_scrapper_1 = Mock()
        mock_scrapper_1.download_page = AsyncMock(return_value="/test/output/success.html")
        
        # Second search fails
        mock_scrapper_2 = Mock()
        mock_scrapper_2.download_page = AsyncMock(side_effect=Exception("Second search failed"))
        
        mock_factory.create_booking_search_scrapper.side_effect = [mock_scrapper_1, mock_scrapper_2]
        search_service.factory = mock_factory

        # Perform first search (success)
        result1 = await search_service.search(sample_request, "./test_output")
        
        # Perform second search (failure)
        result2 = await search_service.search(sample_request, "./test_output")

        # Verify results
        assert result1.success is True
        assert result1.error is None
        
        assert result2.success is False
        assert result2.error == "Second search failed"

        # Verify both searches are tracked correctly
        statuses = search_service.get_search_statuses()
        assert len(statuses) == 2
        
        # First search status
        status1 = statuses[0]
        assert status1.status == "completed"
        assert status1.error == ""
        assert status1.output_path == "/test/output/success.html"
        
        # Second search status
        status2 = statuses[1]
        assert status2.status == "failed"
        assert status2.error == "Second search failed"
        assert status2.output_path == ""

    def test_search_status_elapsed_time_calculation(self, search_service):
        """Test that elapsed_time property works correctly for both success and failure cases."""
        # Create a mock status with completed_at timestamp
        from datetime import datetime, timedelta
        
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=30)
        
        # Add a completed search status
        search_service.search_statuses.append({
            "id": "1",
            "area": "Test Area",
            "guests": "2",
            "period": "2025-07-01 to 2025-07-08",
            "status": "completed",
            "started_at": start_time.isoformat(),
            "completed_at": end_time.isoformat(),
            "output_path": "/test/path.html",
            "error": ""
        })
        
        # Add a failed search status
        search_service.search_statuses.append({
            "id": "2",
            "area": "Test Area",
            "guests": "2", 
            "period": "2025-07-01 to 2025-07-08",
            "status": "failed",
            "started_at": start_time.isoformat(),
            "completed_at": end_time.isoformat(),
            "output_path": "",
            "error": "Test error"
        })

        statuses = search_service.get_search_statuses()
        
        # Both should have elapsed_time calculated
        assert statuses[0].elapsed_time == "30.0"
        assert statuses[1].elapsed_time == "30.0"
